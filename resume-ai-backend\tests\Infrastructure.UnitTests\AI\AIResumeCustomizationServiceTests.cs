using System.Text.Json;
using FluentAssertions;
using Infrastructure.AI;
using Application.Abstractions.AI;

namespace Infrastructure.UnitTests.AI;

public class AIResumeCustomizationServiceTests
{

    [Fact]
    public void ParseAIResponse_WithMarkdownCodeBlocks_ShouldParseCorrectly()
    {
        // Arrange
        const string markdownWrappedJson = """
            ```json
            {
              "customizedContent": "<!DOCTYPE html><html><body><h1>Test Resume</h1></body></html>",
              "summary": "Test summary",
              "confidence": 0.95
            }
            ```
            """;

        // Act
        var result = InvokeParseAIResponse(markdownWrappedJson);

        // Assert
        result.Should().NotBeNull();
        result.CustomizedResumeContent.Should().Be("<!DOCTYPE html><html><body><h1>Test Resume</h1></body></html>");
        result.CustomizationSummary.Should().Be("Test summary");
        result.ConfidenceScore.Should().Be(0.95);
    }

    [Fact]
    public void ParseAIResponse_WithLargeHtmlContent_ShouldParseCorrectly()
    {
        // Arrange - This simulates the actual large HTML content that was causing the issue
        string largeHtmlContent = new string('a', 5000); // 5000 characters
        string markdownWrappedJson = $$"""
            ```json
            {
              "customizedContent": "<!DOCTYPE html><html><body>{{largeHtmlContent}}</body></html>",
              "summary": "Large content test",
              "confidence": 0.85
            }
            ```
            """;

        // Act
        var result = InvokeParseAIResponse(markdownWrappedJson);

        // Assert
        result.Should().NotBeNull();
        result.CustomizedResumeContent.Should().Contain(largeHtmlContent);
        result.CustomizationSummary.Should().Be("Large content test");
        result.ConfidenceScore.Should().Be(0.85);
    }

    [Fact]
    public void ParseAIResponse_WithPlainJson_ShouldParseCorrectly()
    {
        // Arrange
        const string plainJson = """
            {
              "customizedContent": "<!DOCTYPE html><html><body><h1>Test Resume</h1></body></html>",
              "summary": "Test summary",
              "confidence": 0.85
            }
            """;

        // Act
        var result = InvokeParseAIResponse(plainJson);

        // Assert
        result.Should().NotBeNull();
        result.CustomizedResumeContent.Should().Be("<!DOCTYPE html><html><body><h1>Test Resume</h1></body></html>");
        result.CustomizationSummary.Should().Be("Test summary");
        result.ConfidenceScore.Should().Be(0.85);
    }

    [Fact]
    public void ParseAIResponse_WithInvalidJson_ShouldReturnFallback()
    {
        // Arrange
        const string invalidJson = "This is not valid JSON";

        // Act
        var result = InvokeParseAIResponse(invalidJson);

        // Assert
        result.Should().NotBeNull();
        result.CustomizedResumeContent.Should().Be(invalidJson);
        result.CustomizationSummary.Should().Be("AI customization completed (fallback parsing)");
        result.ConfidenceScore.Should().Be(0.6);
    }

    [Fact]
    public void ParseAIResponse_WithMarkdownCodeBlocksButInvalidJson_ShouldReturnFallback()
    {
        // Arrange
        const string markdownWithInvalidJson = """
            ```json
            { invalid json content
            ```
            """;

        // Act
        var result = InvokeParseAIResponse(markdownWithInvalidJson);

        // Assert
        result.Should().NotBeNull();
        result.CustomizedResumeContent.Should().Be(markdownWithInvalidJson);
        result.CustomizationSummary.Should().Be("AI customization completed (fallback parsing)");
        result.ConfidenceScore.Should().Be(0.6);
    }

    [Theory]
    [InlineData("```json\n{\"test\": \"value\"}\n```")]
    [InlineData("```\n{\"test\": \"value\"}\n```")]
    [InlineData("```JSON\n{\"test\": \"value\"}\n```")]
    public void CleanMarkdownCodeBlocks_WithVariousFormats_ShouldCleanCorrectly(string input)
    {
        // Act
        var result = InvokeCleanMarkdownCodeBlocks(input);

        // Assert
        result.Should().Be("{\"test\": \"value\"}");
    }

    // Helper method to access private ParseAIResponse method via reflection
    private AICustomizationResponse InvokeParseAIResponse(string aiResponse)
    {
        var method = typeof(AIResumeCustomizationService)
            .GetMethod("ParseAIResponse", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
        
        return (AICustomizationResponse)method!.Invoke(null, new object[] { aiResponse })!;
    }

    // Helper method to access private CleanMarkdownCodeBlocks method via reflection
    private string InvokeCleanMarkdownCodeBlocks(string response)
    {
        var method = typeof(AIResumeCustomizationService)
            .GetMethod("CleanMarkdownCodeBlocks", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
        
        return (string)method!.Invoke(null, new object[] { response })!;
    }
}
