using Application.Abstractions.AI;
using Application.Abstractions.Data;
using Domain.Jobs;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Infrastructure.BackgroundJobs;

public class AIResumeCustomizationJob
{
    private readonly IAIResumeCustomizationService _aiService;
    private readonly IApplicationDbContext _dbContext;
    private readonly ILogger<AIResumeCustomizationJob> _logger;

    public AIResumeCustomizationJob(
        IAIResumeCustomizationService aiService,
        IApplicationDbContext dbContext,
        ILogger<AIResumeCustomizationJob> logger)
    {
        _aiService = aiService;
        _dbContext = dbContext;
        _logger = logger;
    }

    [AutomaticRetry(Attempts = 2, DelaysInSeconds = new[] { 60, 300 })]
    public async Task ProcessAICustomizationAsync(Guid jobId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting AI resume customization background job for Job ID: {JobId}", jobId);

        try
        {
            // Retrieve the job from database
            var job = await _dbContext.Jobs
                .FirstOrDefaultAsync(j => j.Id == jobId, cancellationToken);

            if (job == null)
            {
                _logger.LogError("Job with ID {JobId} not found", jobId);
                return;
            }

            // We need to get the parent resume from the domain event context
            // For now, let's get the user's parent resume (the one that's not customized)
            var parentResume = await _dbContext.Resumes
                .Where(r => r.UserId == job.UserId && r.ParentId == null && !r.IsDeleted)
                .FirstOrDefaultAsync(cancellationToken);

            if (parentResume == null)
            {
                _logger.LogError("No parent resume found for user {UserId} of job {JobId}", job.UserId, jobId);
                return;
            }

            // Check if job is already processed
            if (job.Status == JobStatus.AIProcessingCompleted)
            {
                _logger.LogInformation("Job {JobId} is already processed", jobId);
                return;
            }

            // Update job status to processing
            job.UpdateStatus(JobStatus.AIProcessing);
            await _dbContext.SaveChangesAsync(cancellationToken);

            // Create AI customization request
            var aiRequest = new AICustomizationRequest(
                job.JobTitle,
                job.JobDescription,
                job.CompanyUrl,
                parentResume.ResumeContent);

            // Process AI customization (this can take several minutes)
            var result = await _aiService.CustomizeResumeAsync(aiRequest, cancellationToken);

            if (result.IsSuccess)
            {
                // Update job with AI results
                job.UpdateAICustomization(
                    result.Value.CustomizedResumeContent,
                    result.Value.CustomizationSummary,
                    result.Value.ConfidenceScore);

                job.UpdateStatus(JobStatus.AIProcessingCompleted);

                _logger.LogInformation(
                    "AI resume customization completed successfully for Job ID: {JobId} with confidence: {Confidence}",
                    jobId, result.Value.ConfidenceScore);
            }
            else
            {
                // Update job status to failed
                job.UpdateStatus(JobStatus.AIProcessingFailed);

                _logger.LogError(
                    "AI resume customization failed for Job ID: {JobId}. Error: {Error}",
                    jobId, result.Error.Description);
            }

            // Save changes to database
            await _dbContext.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("AI resume customization background job completed for Job ID: {JobId}", jobId);
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("AI resume customization job for Job ID: {JobId} was cancelled", jobId);
            
            // Update job status to failed due to cancellation
            await UpdateJobStatusSafely(jobId, JobStatus.AIProcessingFailed, cancellationToken);
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error in AI resume customization job for Job ID: {JobId}", jobId);
            
            // Update job status to failed
            await UpdateJobStatusSafely(jobId, JobStatus.AIProcessingFailed, cancellationToken);
            throw;
        }
    }

    private async Task UpdateJobStatusSafely(Guid jobId, JobStatus status, CancellationToken cancellationToken)
    {
        try
        {
            var job = await _dbContext.Jobs.FirstOrDefaultAsync(j => j.Id == jobId, cancellationToken);
            if (job != null)
            {
                job.UpdateStatus(status);
                await _dbContext.SaveChangesAsync(cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update job status for Job ID: {JobId}", jobId);
        }
    }
}
