using Application.Abstractions.BackgroundJobs;
using Application.Abstractions.Data;
using Domain.Jobs;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SharedKernel;

namespace Application.Jobs.EventHandlers;

internal sealed class JobCreatedForAIProcessingDomainEventHandler(
    IApplicationDbContext context,
    IBackgroundJobService backgroundJobService,
    ILogger<JobCreatedForAIProcessingDomainEventHandler> logger)
    : IDomainEventHandler<JobCreatedForAIProcessingDomainEvent>
{
    public async Task Handle(JobCreatedForAIProcessingDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Enqueuing AI customization background job for Job {JobId}, User {UserId}, Resume {ResumeId}",
                domainEvent.JobId, domainEvent.UserId, domainEvent.ParentResumeId);

            // Verify job exists before enqueuing background job
            Job? job = await context.Jobs
                .AsNoTracking()
                .FirstOrDefaultAsync(j => j.Id == domainEvent.JobId && !j.IsDeleted, cancellationToken);

            if (job is null)
            {
                logger.LogWarning("Job {JobId} not found, skipping AI processing", domainEvent.JobId);
                return;
            }

            // Update job status to indicate AI processing is queued
            var jobToUpdate = await context.Jobs
                .FirstOrDefaultAsync(j => j.Id == domainEvent.JobId, cancellationToken);

            if (jobToUpdate is not null)
            {
                jobToUpdate.UpdateStatus(JobStatus.AIProcessingQueued);
                await context.SaveChangesAsync(cancellationToken);
            }

            // Enqueue background job for AI processing
            // The job will run in the background without timeout constraints
            string backgroundJobId = backgroundJobService.EnqueueAIResumeCustomization(domainEvent.JobId);

            logger.LogInformation("Successfully enqueued AI customization background job {BackgroundJobId} for Job {JobId}",
                backgroundJobId, domainEvent.JobId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to enqueue AI processing background job for Job {JobId}", domainEvent.JobId);
        }
    }
}
