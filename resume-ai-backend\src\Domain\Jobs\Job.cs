namespace Domain.Jobs;
public class Job : BaseEntity
{
    public Guid UserId { get; set; }
    public string JobTitle { get; set; }
    public string JobDescription { get; set; }
    public string JobPostingUrl { get; set; }
    public string CompanyUrl { get; set; }
    public DateTime? AppliedAt { get; set; }

    // AI Processing Status
    public JobStatus Status { get; set; } = JobStatus.Created;

    // AI Customization Results
    public string? AICustomizedContent { get; set; }
    public string? AICustomizationSummary { get; set; }
    public double? AIConfidenceScore { get; set; }
    public DateTime? AIProcessedAt { get; set; }

    public void UpdateStatus(JobStatus newStatus)
    {
        if (Status != newStatus)
        {
            JobStatus oldStatus = Status;
            Status = newStatus;

            Raise(new JobStatusChangedDomainEvent(Id, oldStatus, newStatus));
        }
    }

    public void UpdateAICustomization(string customizedContent, string summary, double confidenceScore)
    {
        AICustomizedContent = customizedContent;
        AICustomizationSummary = summary;
        AIConfidenceScore = confidenceScore;
        AIProcessedAt = DateTime.UtcNow;

        Raise(new JobAICustomizationCompletedDomainEvent(Id, confidenceScore));
    }
}
