﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class AddHangfire : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<double>(
                name: "ai_confidence_score",
                schema: "public",
                table: "jobs",
                type: "double precision",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ai_customization_summary",
                schema: "public",
                table: "jobs",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ai_customized_content",
                schema: "public",
                table: "jobs",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ai_processed_at",
                schema: "public",
                table: "jobs",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "status",
                schema: "public",
                table: "jobs",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ai_confidence_score",
                schema: "public",
                table: "jobs");

            migrationBuilder.DropColumn(
                name: "ai_customization_summary",
                schema: "public",
                table: "jobs");

            migrationBuilder.DropColumn(
                name: "ai_customized_content",
                schema: "public",
                table: "jobs");

            migrationBuilder.DropColumn(
                name: "ai_processed_at",
                schema: "public",
                table: "jobs");

            migrationBuilder.DropColumn(
                name: "status",
                schema: "public",
                table: "jobs");
        }
    }
}
