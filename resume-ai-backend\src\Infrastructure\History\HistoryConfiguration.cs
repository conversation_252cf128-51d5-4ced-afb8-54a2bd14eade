using Domain.History;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.History;

internal sealed class HistoryConfiguration : IEntityTypeConfiguration<Domain.History.History>
{
    public void Configure(EntityTypeBuilder<Domain.History.History> builder)
    {
        builder.HasKey(h => h.Id);

        builder.Property(h => h.Id).ValueGeneratedOnAdd();

        builder.Property(h => h.TableName)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(h => h.PropertyName)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(h => h.PropertyType)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(h => h.PreviousValue)
            .HasColumnType("text"); // Use TEXT type for large content

        builder.Property(h => h.CurrentValue)
            .HasColumnType("text"); // Use TEXT type for large content

        // Index for better query performance
        builder.HasIndex(h => new { h.TableName, h.CreatedAt })
            .HasDatabaseName("ix_histories_table_name_created_at");
    }
}
