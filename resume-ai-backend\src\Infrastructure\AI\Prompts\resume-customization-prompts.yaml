# AI Resume Customization Prompts Configuration
# This file contains all prompts and instructions used for AI-powered resume customization

prompts:
  resume_customization:
    system_message: |
      You are an expert resume writer and career consultant with over 10 years of experience. 
      Your task is to customize resumes to match specific job requirements while maintaining 
      authenticity and accuracy. You understand various industries, job roles, and what 
      recruiters look for in candidates.

    user_prompt_template: |
      Please customize the following resume to better match the job requirements.

      **Job Details:**
      - Position: {job_title}
      - Company: {company_url}
      - Job Description: {job_description}

      **Original Resume Content:**
      {original_resume_content}

      **Instructions:**
      {instructions}

      **Response Format:**
      {response_format}

    instructions: |
      1. Customize the resume to highlight relevant skills and experiences for this specific job
      2. Maintain all factual information - do not add false experiences or skills
      3. Reorganize and emphasize content to better match job requirements
      4. Use keywords from the job description where appropriate and truthful
      5. Keep the same HTML structure and formatting
      6. Prioritize the most relevant experiences and skills at the top of each section
      7. Adjust the professional summary to align with the job requirements
      8. Ensure the customization feels natural and authentic
      9. Maintain proper grammar and professional language throughout
      10. Focus on quantifiable achievements that relate to the job requirements

    response_format: |
      IMPORTANT: Respond with ONLY a valid JSON object. Do NOT wrap your response in markdown code blocks or any other formatting.

      Return a JSON object with these exact properties:
      - "customizedContent": The customized resume HTML content
      - "summary": A brief summary of changes made (max 200 characters)
      - "confidence": A confidence score (0.0-1.0) for the customization quality
      - "keyChanges": An array of key changes made to the resume

      Your response must start with { and end with } - no markdown, no code blocks, no additional text.

      Example format:
      {
        "customizedContent": "customized resume HTML here",
        "summary": "Emphasized relevant skills and reorganized experience section",
        "confidence": 0.85,
        "keyChanges": [
          "Moved relevant experience to top",
          "Added industry keywords",
          "Emphasized technical skills"
        ]
      }

    fallback_instructions: |
      If you cannot parse the job description or resume content properly:
      1. Return the original resume content unchanged
      2. Set confidence to 0.1
      3. Provide a clear explanation in the summary
      4. Include an empty keyChanges array

# Configuration for different job categories
job_categories:
  technology:
    additional_instructions: |
      - Emphasize technical skills, programming languages, and frameworks
      - Highlight relevant projects and their technical impact
      - Focus on problem-solving abilities and innovation
      - Include metrics like performance improvements, user growth, etc.

  marketing:
    additional_instructions: |
      - Emphasize campaign results, ROI, and growth metrics
      - Highlight creative projects and brand building experience
      - Focus on data analysis and customer insights
      - Include social media and digital marketing experience

  finance:
    additional_instructions: |
      - Emphasize analytical skills and financial modeling experience
      - Highlight compliance and risk management experience
      - Focus on cost savings and revenue generation
      - Include relevant certifications and regulatory knowledge

  healthcare:
    additional_instructions: |
      - Emphasize patient care and clinical experience
      - Highlight relevant certifications and continuing education
      - Focus on quality improvement and patient outcomes
      - Include experience with healthcare systems and regulations

# Quality control parameters
quality_control:
  min_confidence_threshold: 0.6
  max_content_length: 50000  # Maximum characters in customized content
  required_sections:
    - "professional summary"
    - "experience"
    - "skills"
  
  validation_rules:
    - "Must maintain original HTML structure"
    - "Cannot add fictional experiences"
    - "Must preserve contact information"
    - "Should improve keyword relevance"

# Error handling messages
error_messages:
  invalid_job_description: "Job description is too short or unclear to provide meaningful customization"
  invalid_resume_content: "Resume content is malformed or too short to customize effectively"
  processing_timeout: "AI processing took too long, returning original content"
  api_error: "AI service temporarily unavailable, please try again later"
