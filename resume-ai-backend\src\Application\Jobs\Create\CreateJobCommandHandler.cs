using Application.Abstractions.Authentication;
using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Domain.Jobs;
using Domain.Resumes;
using Domain.Users;
using Microsoft.EntityFrameworkCore;
using SharedKernel;

namespace Application.Jobs.Create;

internal sealed class CreateJobCommandHandler(
    IApplicationDbContext context,
    IUserContext userContext)
    : ICommandHandler<CreateJobCommand, Guid>
{
    public async Task<Result<Guid>> Handle(CreateJobCommand command, CancellationToken cancellationToken)
    {
        // Validate user exists
        User? user = await context.Users.AsNoTracking()
            .SingleOrDefaultAsync(u => u.Id == userContext.UserId, cancellationToken);

        if (user is null)
        {
            return Result.Failure<Guid>(UserErrors.NotFound(userContext.UserId));
        }

        // Check for duplicate job (same title and company for the user)
        bool duplicateExists = await context.Jobs
            .AnyAsync(j => j.UserId == userContext.UserId &&
                          j.JobTitle == command.JobTitle &&
                          j.CompanyUrl == command.CompanyUrl &&
                          !j.IsDeleted, cancellationToken);

        if (duplicateExists)
        {
            return Result.Failure<Guid>(JobErrors.DuplicateJob(command.JobTitle, command.CompanyUrl));
        }

        // Get the user's parent resume for AI processing
        Resume? parentResume = await context.Resumes
            .AsNoTracking()
            .FirstOrDefaultAsync(r => r.UserId == userContext.UserId &&
                                     (r.ParentId == null || r.ParentId == Guid.Empty) &&
                                     !r.IsDeleted, cancellationToken);

        if (parentResume is null)
        {
            return Result.Failure<Guid>(JobErrors.HasNoBaseResume(user.Id));
        }

        var job = new Job
        {
            UserId = userContext.UserId,
            JobTitle = command.JobTitle,
            JobDescription = command.JobDescription,
            JobPostingUrl = command.JobPostingUrl,
            CompanyUrl = command.CompanyUrl
        };

        job.Raise(new JobCreatedDomainEvent(job.Id));

        // If user has a parent resume, raise AI processing events
        job.Raise(new JobCreatedForAIProcessingDomainEvent(job.Id, userContext.UserId, parentResume.Id));

        context.Jobs.Add(job);
        await context.SaveChangesAsync(cancellationToken);

        return job.Id;
    }
}
